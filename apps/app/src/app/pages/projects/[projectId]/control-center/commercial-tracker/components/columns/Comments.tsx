import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeDetailsBasicSchema,
} from '@shape-construction/api/src';
import { InputTextArea } from '@shape-construction/arch-ui';
import React from 'react';

type CommentsProps = {
  record: PotentialChangeDetailsBasicSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Comments: React.FC<CommentsProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  return (
    <InputTextArea
      placeholder="Add comment..."
      className="bg-transparent !text-xs leading-4 font-normal text-neutral-bold border-none shadow-none truncate px-2.5 py-1 rounded-sm focus:ring-2 hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
      rows={1}
      name="comment"
      value={record.comment || undefined}
      onBlur={(e: React.FocusEvent<HTMLTextAreaElement>) => {
        if (e.target.value !== record.comment) {
          onUpdatePotentialChangeRecord({ comment: e.target.value });
        }
      }}
    />
  );
};
