import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema, PotentialChangeSchema } from '@shape-construction/api/src';
import { Button } from '@shape-construction/arch-ui';
import { DocumentArrowDownIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { canLinkChangeSignals, canUnlinkChangeSignals } from '../utils/potentialChangeActions';
import { ChangeDetailsEditableSection } from './ChangeDetailsEditableSection';
import { ChangeDetailsHeader } from './ChangeDetailsHeader';
import { PotentialChangeSignalItem } from './PotentialChangeSignalItem';

type PotentialChangeSignalsListProps = {
  potentialChange: PotentialChangeSchema;
  onLinkChangeSignalsClick: () => void;
  onUnlinkChangeSignalClick: (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => void;
  isUnlinkingChangeSignals: boolean;
};

export function PotentialChangeSignalsList({
  potentialChange,
  onLinkChangeSignalsClick,
  onUnlinkChangeSignalClick,
  isUnlinkingChangeSignals,
}: PotentialChangeSignalsListProps) {
  const messages = useMessageGetter('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer');
  const fields = useMessageGetter('controlCenter.commercialTracker.fields');
  const hasLinks = !!potentialChange.signalsCount;

  const linkSignalsButton = canLinkChangeSignals(potentialChange) ? (
    <Button
      color="primary"
      size="xs"
      variant="outlined"
      leadingIcon={PlusCircleIcon}
      onClick={onLinkChangeSignalsClick}
    >
      {messages('linkSignals')}
    </Button>
  ) : null;

  return (
    <div className="flex flex-col grow">
      <div className="flex flex-col grow">
        <ChangeDetailsHeader change={potentialChange} />
        <ChangeDetailsEditableSection change={potentialChange} />
        {hasLinks ? (
          <div className="flex flex-col gap-3 py-4">
            <div className="flex justify-between items-center px-4">
              <h4 className="text-sm leading-5 font-medium text-neutral-bold grow">{fields('linkedSignals')}</h4>
              {linkSignalsButton}
            </div>
            <ul className="flex flex-col gap-2 px-4" aria-label={messages('changeSignalsList.changeSignals')}>
              {potentialChange.signals.map((signal) => (
                <li key={signal.signalId}>
                  {
                    <PotentialChangeSignalItem
                      signal={signal}
                      onUnlinkChangeSignalClick={onUnlinkChangeSignalClick}
                      isUnlinkingChangeSignals={isUnlinkingChangeSignals}
                      canUnlink={canUnlinkChangeSignals(potentialChange)}
                    />
                  }
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="flex flex-col grow items-center justify-center gap-6">
            <div className="flex flex-col items-center justify-center gap-2">
              <DocumentArrowDownIcon className="h-10 w-10 text-icon-neutral-subtle" />
              <p className="text-base leading-6 font-medium text-neutral-bold">{messages('emptyState.title')}</p>
              <p className="text-sm leading-5 font-normal text-neutral-subtle">{messages('emptyState.subtitle')}</p>
            </div>
            {linkSignalsButton}
          </div>
        )}
      </div>
    </div>
  );
}
