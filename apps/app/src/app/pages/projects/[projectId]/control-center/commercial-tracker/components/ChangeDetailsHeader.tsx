import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { Avatar } from '@shape-construction/arch-ui';
import { formatDate } from '@shape-construction/utils/DateTime';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectPerson } from 'app/queries/projects/people';

type ChangeDetailsHeaderProps = {
  change: PotentialChangeSchema;
};
export const ChangeDetailsHeader: React.FC<ChangeDetailsHeaderProps> = ({ change }) => {
  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const { data: author } = useProjectPerson(project.id, change.teamMemberId);
  return (
    <div className="flex flex-col gap-2 px-4 py-6 border-b">
      <h1 className="text-xl leading-7 font-medium text-neutral-bold">{change.title}</h1>
      <div className="flex gap-6 text-xs leading-4 font-normal text-neutral-subtle">
        <div className="flex gap-2">
          <span>{messages('author')}</span>
          <Avatar text="MO" size="xs" imgURL={author?.user.avatarUrl} />
          <span>{author?.user.name}</span>
        </div>
        <div className="flex gap-2">
          <span className="">{messages('date')}</span>
          <span className="text-neutral">{formatDate(change.createdAt, project.timezone, 'DD-MMM-YYYY')}</span>
        </div>
      </div>
    </div>
  );
};
