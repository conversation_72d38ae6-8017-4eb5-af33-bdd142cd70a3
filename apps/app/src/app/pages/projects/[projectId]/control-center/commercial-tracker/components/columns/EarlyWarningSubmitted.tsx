import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeDetailsBasicSchema,
} from '@shape-construction/api/src';
import { Select } from '@shape-construction/arch-ui';

type EarlyWarningSubmittedProps = {
  record: PotentialChangeDetailsBasicSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const EarlyWarningSubmitted: React.FC<EarlyWarningSubmittedProps> = ({
  record,
  onUpdatePotentialChangeRecord,
}) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');

  const getDisplayText = () => {
    if (record.earlyWarningNoticeSubmitted === null) {
      return messages('select');
    }
    return record.earlyWarningNoticeSubmitted
      ? messages('options.earlyWarningNoticeSubmitted.yes')
      : messages('options.earlyWarningNoticeSubmitted.no');
  };

  return (
    <Select.Root
      onChange={(value: boolean) => {
        if (value !== record.earlyWarningNoticeSubmitted) {
          onUpdatePotentialChangeRecord({ early_warning_notice_submitted: value });
        }
      }}
      value={record.earlyWarningNoticeSubmitted}
      className="border-none"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.earlyWarningNoticeSubmitted')}
        variant="plain"
        showChevronOnHover
      >
        <span className="text-xs leading-4 font-normal text-neutral-bold">{getDisplayText()}</span>
      </Select.Trigger>

      <Select.ResponsivePanel className="border-none" portal>
        <Select.Options>
          <Select.Option value={true}>
            <Select.OptionText className="text-xs">
              {messages('options.earlyWarningNoticeSubmitted.yes')}
            </Select.OptionText>
          </Select.Option>
          <Select.Option value={false}>
            <Select.OptionText className="text-xs">
              {messages('options.earlyWarningNoticeSubmitted.no')}
            </Select.OptionText>
          </Select.Option>
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
